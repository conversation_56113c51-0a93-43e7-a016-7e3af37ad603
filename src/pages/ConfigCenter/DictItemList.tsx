import {createResource, createSignal, For, Show} from 'solid-js';
import {dictItemApi} from './api';
import type {DictItem} from './types';
import { TableLoading, SpinnerLoading } from '../../components/Loading';

interface Props {
    dict: string;
    disabled?: boolean;
    loading?: boolean;
}

export default function DictItemList(props: Props) {
    const [page, setPage] = createSignal(1);
    const [pageSize] = createSignal(10);
    const [editing, setEditing] = createSignal<null | DictItem>(null);
    const [showForm, setShowForm] = createSignal(false);
    const [form, setForm] = createSignal<Partial<DictItem>>({dict: props.dict});

    const [data, {refetch}] = createResource(
        () => [props.dict, page(), pageSize()] as [string, number, number],
        ([dict, page, pageSize]) => dictItemApi.list(dict, page, pageSize),
        {initialValue: {list: [], pagination: {page: 1, pageSize: 10, total: 0}}}
    );

    function handleEdit(item: DictItem) {
        setEditing(item);
        setForm({...item});
        setShowForm(true);
    }

    function handleAdd() {
        setEditing(null);
        setForm({dict: props.dict});
        setShowForm(true);
    }

    async function handleSubmit(e: Event) {
        e.preventDefault();
        if (editing()) {
            await dictItemApi.update(editing()!.id, form() as any);
        } else {
            await dictItemApi.add(form() as any);
        }
        setShowForm(false);
        refetch();
    }

    async function handleDelete(id: string) {
        if (window.confirm('确定删除该小类？')) {
            await dictItemApi.remove(id);
            refetch();
        }
    }

    return (
        <div class="min-h-[70vh] flex flex-col">
            <div class="flex justify-between items-center mb-4">
                <span class="font-bold text-lg">字典小类</span>
                <button class="btn btn-primary btn-sm" onClick={handleAdd} disabled={props.disabled}>新增</button>
            </div>
            <Show when={props.dict} fallback={<div>请先选择左侧大类</div>}>
                <Show when={!props.loading && !data.loading} fallback={<TableLoading columns={5} />}>
                    <div class="overflow-x-auto">
                        <table class="table table-zebra w-full">
                            <colgroup>
                                <col style={{width: '15%'}}/>
                                <col style={{width: '25%'}}/>
                                <col style={{width: '20%'}}/>
                                <col style={{width: '15%'}}/>
                                <col style={{width: '25%'}}/>
                            </colgroup>
                            <thead>
                            <tr>
                                <th class="text-left">key</th>
                                <th class="text-left">value</th>
                                <th class="text-left">描述</th>
                                <th class="text-left">排序</th>
                                <th class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <For each={data()!.list}>
                                {(item) => (
                                    <tr class="align-middle">
                                        <td class="text-left">{item.key}</td>
                                        <td class="text-left">{item.value}</td>
                                        <td class="text-left">{item.describe}</td>
                                        <td class="text-left">{item.itemOrder}</td>
                                        <td class="text-left">
                                            <button class="btn btn-outline btn-xs mr-2"
                                                    onClick={() => handleEdit(item)}>编辑
                                            </button>
                                            <button class="btn btn-outline btn-error btn-xs"
                                                    onClick={() => handleDelete(item.id)}>删除
                                            </button>
                                        </td>
                                    </tr>
                                )}
                            </For>
                            </tbody>
                        </table>
                    </div>
                    {/* 分页 */}
                    <div class="mt-auto py-4 flex flex-col items-center gap-2">
                        <div class="text-sm text-gray-400">
                            共 {data()!.pagination.total} 条，每页 {pageSize()} 条
                        </div>
                        <div class="join">
                            <button class="btn btn-sm btn-outline join-item" disabled={page() === 1}
                                    onClick={() => setPage(page() - 1)}>上一页
                            </button>
                            <button
                                class="btn btn-sm btn-ghost join-item cursor-default">{page()} / {Math.ceil(data()!.pagination.total / pageSize())}</button>
                            <button class="btn btn-sm btn-outline join-item"
                                    disabled={page() === Math.ceil(data()!.pagination.total / pageSize())}
                                    onClick={() => setPage(page() + 1)}>下一页
                            </button>
                        </div>
                    </div>
                </Show>
            </Show>
            {/* 新增/编辑表单弹窗 */}
            <Show when={showForm()}>
                <dialog open class="modal modal-open">
                    <div class="modal-box">
                        <h3 class="font-bold text-lg mb-4">{editing() ? '编辑小类' : '新增小类'}</h3>
                        <form onSubmit={handleSubmit} class="space-y-4">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">key</span>
                                </label>
                                <input required class="input input-bordered w-full" value={form().key || ''}
                                       onInput={e => setForm(f => ({...f, key: e.currentTarget.value}))}/>
                            </div>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">value</span>
                                </label>
                                <input required class="input input-bordered w-full" value={form().value || ''}
                                       onInput={e => setForm(f => ({...f, value: e.currentTarget.value}))}/>
                            </div>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">描述</span>
                                </label>
                                <input class="input input-bordered w-full" value={form().describe || ''}
                                       onInput={e => setForm(f => ({...f, describe: e.currentTarget.value}))}/>
                            </div>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">排序</span>
                                </label>
                                <input type="number" class="input input-bordered w-full" value={form().itemOrder || ''}
                                       onInput={e => setForm(f => ({...f, itemOrder: Number(e.currentTarget.value)}))}/>
                            </div>
                            <div class="modal-action">
                                <button class="btn btn-primary" type="submit">保存</button>
                                <button class="btn" type="button" onClick={() => setShowForm(false)}>取消</button>
                            </div>
                        </form>
                    </div>
                    <form method="dialog" class="modal-backdrop">
                        <button onClick={() => setShowForm(false)}>关闭</button>
                    </form>
                </dialog>
            </Show>
        </div>
    );
} 