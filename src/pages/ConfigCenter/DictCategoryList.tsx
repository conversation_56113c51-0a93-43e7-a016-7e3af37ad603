import {createResource, createSignal, For, Show} from 'solid-js';
import {dictCategoryApi} from './api';
import type {DictCategory} from './types';
import {TableLoading} from '../../components/Loading';

interface Props {
    onSelect: (cat: DictCategory) => void;
    selectedId?: string;
}

export default function DictCategoryList(props: Props) {
    const [page, setPage] = createSignal(1);
    const [pageSize] = createSignal(10);
    const [editing, setEditing] = createSignal<null | DictCategory>(null);
    const [showForm, setShowForm] = createSignal(false);
    const [form, setForm] = createSignal<Partial<DictCategory>>({});

    const [data, {refetch}] = createResource(
        () => [page(), pageSize()] as [number, number],
        ([page, pageSize]) => dictCategoryApi.list(page, pageSize)
    );

    function handleEdit(cat: DictCategory) {
        setEditing(cat);
        setForm({...cat});
        setShowForm(true);
    }

    function handleAdd() {
        setEditing(null);
        setForm({});
        setShowForm(true);
    }

    async function handleSubmit(e: Event) {
        e.preventDefault();
        if (editing()) {
            await dictCategoryApi.update(editing()!.id, form() as any);
        } else {
            await dictCategoryApi.add(form() as any);
        }
        setShowForm(false);
        refetch();
    }

    async function handleDelete(id: string) {
        if (window.confirm('确定删除该大类及其所有小类？')) {
            await dictCategoryApi.remove(id);
            refetch();
        }
    }

    return (
        <div class="min-h-[70vh] flex flex-col">
            <div class="flex justify-between items-center mb-4">
                <span class="font-bold text-lg">字典大类</span>
                <button class="btn btn-primary btn-sm" onClick={handleAdd}>新增</button>
            </div>
            <Show when={!data.loading} fallback={<TableLoading columns={2} />}>
                <ul class="space-y-3">
                    <For each={data()!.list}>
                        {(cat) => (
                            <li
                                class={`p-3 rounded-lg cursor-pointer flex items-center border ${props.selectedId === cat.id ? 'border-primary bg-primary/10 font-bold shadow' : 'border-base-200 bg-base-100'}`}
                                onClick={() => props.onSelect(cat)}
                            >
                                <div class="flex-1 min-w-0 flex flex-col justify-center">
                                    <div
                                        class="font-semibold text-base text-base-content break-all leading-tight">{cat.name}</div>
                                    <Show when={cat.describe}>
                                        <div
                                            class="text-gray-400 text-xs mt-1 break-all leading-tight">{cat.describe}</div>
                                    </Show>
                                </div>
                                <div class="flex gap-2 ml-auto items-center min-w-[120px] justify-end"
                                     onClick={e => e.stopPropagation()}>
                                    <button class="btn btn-outline btn-xs" onClick={() => handleEdit(cat)}>编辑</button>
                                    <button class="btn btn-outline btn-error btn-xs"
                                            onClick={() => handleDelete(cat.id)}>删除
                                    </button>
                                </div>
                            </li>
                        )}
                    </For>
                </ul>
                {/* 分页 */}
                <div class="mt-auto py-4 flex flex-col items-center gap-2">
                    <div class="text-sm text-gray-400">
                        共 {data()!.pagination.total} 条，每页 {pageSize()} 条
                    </div>
                    <div class="join">
                        <button class="btn btn-sm btn-outline join-item" disabled={page() === 1}
                                onClick={() => setPage(page() - 1)}>上一页
                        </button>
                        <button
                            class="btn btn-sm btn-ghost join-item cursor-default">{page()} / {Math.ceil(data()!.pagination.total / pageSize())}</button>
                        <button class="btn btn-sm btn-outline join-item"
                                disabled={page() === Math.ceil(data()!.pagination.total / pageSize())}
                                onClick={() => setPage(page() + 1)}>下一页
                        </button>
                    </div>
                </div>
            </Show>
            {/* 新增/编辑表单弹窗 */}
            <Show when={showForm()}>
                <dialog open class="modal modal-open">
                    <div class="modal-box">
                        <h3 class="font-bold text-lg mb-4">{editing() ? '编辑大类' : '新增大类'}</h3>
                        <form onSubmit={handleSubmit} class="space-y-4">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">字典标识(dict)</span>
                                </label>
                                <input required class="input input-bordered w-full" value={form().dict || ''}
                                       onInput={e => setForm(f => ({...f, dict: e.currentTarget.value}))}/>
                            </div>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">名称</span>
                                </label>
                                <input required class="input input-bordered w-full" value={form().name || ''}
                                       onInput={e => setForm(f => ({...f, name: e.currentTarget.value}))}/>
                            </div>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">描述</span>
                                </label>
                                <input class="input input-bordered w-full" value={form().describe || ''}
                                       onInput={e => setForm(f => ({...f, describe: e.currentTarget.value}))}/>
                            </div>
                            <div class="modal-action">
                                <button class="btn btn-primary" type="submit">保存</button>
                                <button class="btn" type="button" onClick={() => setShowForm(false)}>取消</button>
                            </div>
                        </form>
                    </div>
                    <form method="dialog" class="modal-backdrop">
                        <button onClick={() => setShowForm(false)}>关闭</button>
                    </form>
                </dialog>
            </Show>
        </div>
    );
} 