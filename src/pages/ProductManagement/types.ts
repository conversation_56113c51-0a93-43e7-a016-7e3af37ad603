// 商品底版信息
export interface ProductBase {
  id: string;
  name: string;                    // 商品名称
  category: string;                // 商品分类
  type: string;                    // 商品品类
  supplier: string;                // 供应商
  description?: string;            // 商品描述
  createdAt: string;
  updatedAt?: string;
}

// 平台模板基础接口
export interface PlatformTemplateBase {
  id: string;
  productId: string;               // 关联的商品ID
  platform: string;               // 平台类型 (temu, shein, amazon等)
  shopName: string;                // 店铺名称
  templateName: string;            // 模板名称
  createdAt: string;
  updatedAt?: string;
}

// Temu平台样品模板
export interface TemuTemplate extends PlatformTemplateBase {
  platform: 'temu';
  publishTitle: string;            // 发布标题
  shopNumber: string;              // 商铺号
  skc: string;                     // SKC
  origin: string;                  // 产地
  province: string;                // 省份
  currency: string;                // 币种
  goodsProperties: GoodsProperty[]; // 商品属性
  specifications: TemuSpecification[];  // 商品规格信息
  sizeChart: TemuSizeChart;        // 尺码表
}

// Shein平台样品模板
export interface SheinTemplate extends PlatformTemplateBase {
  platform: 'shein';
  productTitle: string;            // 产品标题
  category: string;                // 分类
  brand: string;                   // 品牌
  material: string;                // 材质
  specifications: SheinSpecification[];
}

// Amazon平台样品模板
export interface AmazonTemplate extends PlatformTemplateBase {
  platform: 'amazon';
  asin: string;                    // ASIN
  title: string;                   // 标题
  bulletPoints: string[];          // 要点
  description: string;             // 描述
  keywords: string[];              // 关键词
  specifications: AmazonSpecification[];
}

// 平台模板联合类型
export type PlatformTemplate = TemuTemplate | SheinTemplate | AmazonTemplate;

// Temu商品规格信息
export interface TemuSpecification {
  id: string;
  platformSize: string;           // 平台尺码
  declaredPrice: number;          // 申报价格
  weight: number;                 // 重量(g)
}

// Shein商品规格信息
export interface SheinSpecification {
  id: string;
  size: string;                   // 尺码
  color: string;                  // 颜色
  price: number;                  // 价格
  stock: number;                  // 库存
}

// Amazon商品规格信息
export interface AmazonSpecification {
  id: string;
  variant: string;                // 变体
  sku: string;                    // SKU
  price: number;                  // 价格
  quantity: number;               // 数量
}

// 尺码参数选项
export interface SizeParameter {
  key: string;
  label: string;
  unit: string;
}

// 尺码规格选项
export interface SizeSpecOption {
  key: string;
  label: string;
  category: 'standard' | 'numeric' | 'age';
}

// Temu尺码表
export interface TemuSizeChart {
  id: string;
  selectedParameters: string[];   // 选中的尺码参数
  selectedSpecs: string[];        // 选中的尺码规格
  measurements: SizeMeasurement[]; // 尺码测量数据
}

// 尺码测量数据
export interface SizeMeasurement {
  id: string;
  sizeSpec: string;               // 尺码规格
  measurements: { [parameter: string]: number }; // 参数测量值
}

// 平台类型
export interface Platform {
  key: string;
  label: string;
  description: string;
}

// 分页信息
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
}

// API响应格式
export interface ApiResponse<T> {
  code: string;
  message: string;
  data: T;
}

export interface PageResponse<T> {
  list: T[];
  pagination: Pagination;
}
