import { createSignal, createEffect, For, Show } from 'solid-js';
import { platformTemplateApi } from './api';
import type { ProductBase, TemuTemplate, TemuSpecification, TemuSizeChart, GoodsProperty } from './types';
import { PlusIcon, TrashIcon } from '../../assets/Icons.jsx';
import SizeChartConfig from './SizeChartConfig';
import GoodsPropertiesConfig from './GoodsPropertiesConfig';

interface Props {
  product: ProductBase;
  templateName?: string;
  shopName?: string;
  existingTemplate?: TemuTemplate;
  isNew?: boolean;
  onBack?: () => void;
}

export default function TemuTemplate(props: Props) {
  const [form, setForm] = createSignal<Partial<TemuTemplate>>({
    productId: props.product.id,
    platform: 'temu',
    templateName: props.templateName || '',
    shopName: props.shopName || '',
    publishTitle: '',
    shopNumber: '',
    skc: '',
    origin: '中国',
    province: '',
    currency: 'CNY',
    goodsProperties: [],
    specifications: [],
    sizeChart: {
      id: '',
      selectedParameters: [],
      selectedSpecs: [],
      measurements: []
    }
  });

  const [loading, setLoading] = createSignal(false);

  // 当现有模板数据加载完成时，更新表单
  createEffect(() => {
    if (props.existingTemplate) {
      setForm(props.existingTemplate);
    }
  });

  // 当基本信息变化时，更新表单
  createEffect(() => {
    setForm(f => ({
      ...f,
      templateName: props.templateName || '',
      shopName: props.shopName || ''
    }));
  });

  // 添加规格
  function addSpecification() {
    const specs = form().specifications || [];
    const newSpec: TemuSpecification = {
      id: Date.now().toString(),
      platformSize: '',
      declaredPrice: 0,
      weight: 0
    };
    setForm(f => ({
      ...f,
      specifications: [...specs, newSpec]
    }));
  }

  // 删除规格
  function removeSpecification(index: number) {
    const specs = form().specifications || [];
    specs.splice(index, 1);
    setForm(f => ({
      ...f,
      specifications: [...specs]
    }));
  }

  // 更新规格
  function updateSpecification(index: number, field: keyof TemuSpecification, value: any) {
    const specs = form().specifications || [];
    specs[index] = { ...specs[index], [field]: value };
    setForm(f => ({
      ...f,
      specifications: [...specs]
    }));
  }

  // 更新商品属性
  function updateGoodsProperties(goodsProperties: GoodsProperty[]) {
    setForm(f => ({
      ...f,
      goodsProperties
    }));
  }

  // 更新尺码表
  function updateSizeChart(sizeChart: TemuSizeChart) {
    setForm(f => ({
      ...f,
      sizeChart
    }));
  }

  // 保存模板
  async function handleSave() {
    try {
      setLoading(true);

      // 处理商品属性数据，确保按照模板标准保存
      const processedForm = {
        ...form(),
        goodsProperties: (form().goodsProperties || []).map(prop => ({
          templatePid: prop.templatePid,
          refPid: prop.refPid,
          vid: prop.vid,
          specId: prop.specId,
          imgUrl: prop.imgUrl,
          note: prop.note,
          parentSpecId: prop.parentSpecId,
          valueUnitId: prop.valueUnitId,
          groupId: prop.groupId,
          valueUnit: prop.valueUnit,
          numberInputValue: prop.numberInputValue,
          value: prop.value
        }))
      };

      await platformTemplateApi.save(processedForm as any);
      alert('保存成功！');
      if (props.onBack) {
        props.onBack();
      }
    } catch (error) {
      alert('保存失败：' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div class="space-y-6">
      <div class="card bg-base-100 shadow">
        <div class="card-body">
          <h3 class="card-title text-lg mb-4">基本信息</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">发布标题 *</span>
              </label>
              <input 
                type="text"
                class="input input-bordered" 
                value={form().publishTitle || ''}
                onInput={e => setForm(f => ({ ...f, publishTitle: e.currentTarget.value }))}
                placeholder="请输入发布标题"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">商铺号 *</span>
              </label>
              <input 
                type="text"
                class="input input-bordered" 
                value={form().shopNumber || ''}
                onInput={e => setForm(f => ({ ...f, shopNumber: e.currentTarget.value }))}
                placeholder="请输入商铺号"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">SKC *</span>
              </label>
              <input 
                type="text"
                class="input input-bordered" 
                value={form().skc || ''}
                onInput={e => setForm(f => ({ ...f, skc: e.currentTarget.value }))}
                placeholder="请输入SKC"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">产地 *</span>
              </label>
              <input 
                type="text"
                class="input input-bordered" 
                value={form().origin || ''}
                onInput={e => setForm(f => ({ ...f, origin: e.currentTarget.value }))}
                placeholder="请输入产地"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">省份 *</span>
              </label>
              <input 
                type="text"
                class="input input-bordered" 
                value={form().province || ''}
                onInput={e => setForm(f => ({ ...f, province: e.currentTarget.value }))}
                placeholder="请输入省份"
              />
            </div>
            
            <div class="form-control">
              <label class="label">
                <span class="label-text">币种 *</span>
              </label>
              <select 
                class="select select-bordered"
                value={form().currency || 'CNY'}
                onChange={e => setForm(f => ({ ...f, currency: e.currentTarget.value }))}
              >
                <option value="CNY">人民币(CNY)</option>
                <option value="USD">美元(USD)</option>
                <option value="EUR">欧元(EUR)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* 商品属性 */}
      <div class="card bg-base-100 shadow">
        <div class="card-body">
          <h3 class="card-title text-lg mb-4">商品属性</h3>
          <GoodsPropertiesConfig
            goodsProperties={form().goodsProperties || []}
            onUpdate={updateGoodsProperties}
          />
        </div>
      </div>

      {/* 商品规格信息 */}
      <div class="card bg-base-100 shadow">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h3 class="card-title text-lg">商品规格信息</h3>
            <button class="btn btn-primary btn-sm" onClick={addSpecification}>
              <PlusIcon />
              添加规格
            </button>
          </div>
          
          <Show when={(form().specifications || []).length > 0} fallback={
            <div class="text-center py-8 text-base-content/70">
              <div class="text-sm">暂无规格信息，点击"添加规格"按钮添加</div>
            </div>
          }>
            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>平台尺码</th>
                    <th>申报价格</th>
                    <th>重量(g)</th>
                    <th class="text-center">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <For each={form().specifications || []}>
                    {(spec, index) => (
                      <tr>
                        <td>
                          <input 
                            type="text"
                            class="input input-bordered input-sm w-full" 
                            value={spec.platformSize}
                            onInput={e => updateSpecification(index(), 'platformSize', e.currentTarget.value)}
                            placeholder="如：S"
                          />
                        </td>
                        <td>
                          <input 
                            type="number"
                            step="0.01"
                            class="input input-bordered input-sm w-full" 
                            value={spec.declaredPrice}
                            onInput={e => updateSpecification(index(), 'declaredPrice', parseFloat(e.currentTarget.value) || 0)}
                            placeholder="0.00"
                          />
                        </td>
                        <td>
                          <input 
                            type="number"
                            class="input input-bordered input-sm w-full" 
                            value={spec.weight}
                            onInput={e => updateSpecification(index(), 'weight', parseInt(e.currentTarget.value) || 0)}
                            placeholder="0"
                          />
                        </td>
                        <td class="text-center">
                          <button 
                            class="btn btn-outline btn-error btn-xs"
                            onClick={() => removeSpecification(index())}
                          >
                            <TrashIcon />
                          </button>
                        </td>
                      </tr>
                    )}
                  </For>
                </tbody>
              </table>
            </div>
          </Show>
        </div>
      </div>

      {/* 尺码表配置 */}
      <div>
        <h3 class="text-lg font-bold mb-4">尺码表配置</h3>
        <SizeChartConfig
          sizeChart={form().sizeChart || {
            id: '',
            selectedParameters: [],
            selectedSpecs: [],
            measurements: []
          }}
          onUpdate={updateSizeChart}
        />
      </div>

      {/* 保存按钮 */}
      <div class="flex justify-end">
        <button 
          class="btn btn-primary"
          onClick={handleSave}
          disabled={loading()}
        >
          {loading() ? '保存中...' : '保存模板'}
        </button>
      </div>
    </div>
  );
}
