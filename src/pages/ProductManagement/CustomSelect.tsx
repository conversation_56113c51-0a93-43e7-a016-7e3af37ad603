import { createSignal, For, Show, onCleanup } from 'solid-js';
import { getPropertyValueChinese } from './propertyTranslations';

interface Option {
  vid: number;
  value: string;
}

interface Props {
  options: Option[];
  value: string;
  placeholder: string;
  onChange: (value: string) => void;
  class?: string;
}

export default function CustomSelect(props: Props) {
  const [isOpen, setIsOpen] = createSignal(false);
  let dropdownRef: HTMLDivElement | undefined;

  // 点击外部关闭下拉框
  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
      setIsOpen(false);
    }
  };

  // 当组件挂载时添加监听器，卸载时移除
  document.addEventListener('click', handleClickOutside);
  onCleanup(() => document.removeEventListener('click', handleClickOutside));

  const handleSelect = (value: string, event: MouseEvent) => {
    event.stopPropagation();
    props.onChange(value);
    setIsOpen(false);
  };

  const getDisplayText = () => {
    if (!props.value) return props.placeholder;
    const chineseName = getPropertyValueChinese(props.value);
    return `${chineseName} ${props.value}`;
  };

  return (
    <div class={`dropdown ${isOpen() ? 'dropdown-open' : ''} w-full`} ref={dropdownRef}>
      <div
        tabindex="0"
        role="button"
        class={`btn btn-outline btn-sm justify-between w-full ${props.class || ''}`}
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen());
        }}
      >
        <span class={props.value ? 'text-base-content' : 'text-base-content/50'}>
          {getDisplayText()}
        </span>
        <svg 
          class={`w-4 h-4 transition-transform ${isOpen() ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      
      <Show when={isOpen()}>
        <ul 
          tabindex="0" 
          class="dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-base-300 max-h-60 overflow-y-auto"
        >
          <li>
            <a
              class="text-base-content/50 hover:bg-base-200"
              onClick={(e) => handleSelect('', e)}
            >
              {props.placeholder}
            </a>
          </li>
          <For each={props.options}>
            {option => (
              <li>
                <a
                  class={`hover:bg-base-200 ${props.value === option.value ? 'bg-primary/10 text-primary' : ''}`}
                  onClick={(e) => handleSelect(option.value, e)}
                >
                  <span class="flex items-center gap-2">
                    <span class="text-base-content font-medium">
                      {getPropertyValueChinese(option.value)}
                    </span>
                    <span class="text-base-content/50 text-sm">
                      {option.value}
                    </span>
                  </span>
                </a>
              </li>
            )}
          </For>
        </ul>
      </Show>
    </div>
  );
}
