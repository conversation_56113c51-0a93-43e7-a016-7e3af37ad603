import type {PageResponse, PlatformTemplate, ProductBase, SheinTemplate, TemuTemplate, PropertyTemplate} from './types';
import goodsPropertiesData from './goodsPropertiesData.json';

const BASE_URL = '/api/v1/goods';

// Mock数据 - 平台模板
let mockPlatformTemplates: PlatformTemplate[] = [
    {
        id: '1',
        productId: '1',
        platform: 'temu',
        templateName: 'Temu基础款T恤模板',
        shopName: 'Temu旗舰店',
        publishTitle: '基础款纯棉T恤 舒适透气 多色可选',
        shopNumber: 'SHOP001',
        skc: 'SKC001',
        origin: '中国',
        province: '广东省',
        currency: 'CNY',
        goodsProperties: [
            {
                templatePid: 1464648,
                refPid: 12,
                vid: 4,
                specId: null,
                imgUrl: null,
                note: null,
                parentSpecId: null,
                valueUnitId: null,
                groupId: null,
                valueUnit: null,
                numberInputValue: null,
                value: "Cotton",
                name: "Material",
                nameEn: "材质",
                required: true,
                type: 'select' as const
            },
            {
                templatePid: 1464647,
                refPid: 15,
                vid: 35387,
                specId: null,
                imgUrl: null,
                note: "主要成分",
                parentSpecId: null,
                valueUnitId: 57,
                groupId: null,
                valueUnit: "%",
                numberInputValue: "95",
                value: "Cotton",
                name: "Composition",
                nameEn: "成分",
                required: false,
                type: 'select' as const
            }
        ],
        specifications: [
            {id: '1', platformSize: 'S', declaredPrice: 25.00, weight: 150},
            {id: '2', platformSize: 'M', declaredPrice: 25.00, weight: 160},
            {id: '3', platformSize: 'L', declaredPrice: 25.00, weight: 170}
        ],
        sizeChart: {
            id: '1',
            selectedParameters: ['length', 'chest', 'sleeve'],
            selectedSpecs: ['S', 'M', 'L'],
            measurements: [
                {
                    id: '1',
                    sizeSpec: 'S',
                    measurements: {length: 65, chest: 96, sleeve: 20}
                },
                {
                    id: '2',
                    sizeSpec: 'M',
                    measurements: {length: 67, chest: 100, sleeve: 21}
                },
                {
                    id: '3',
                    sizeSpec: 'L',
                    measurements: {length: 69, chest: 104, sleeve: 22}
                }
            ]
        },
        createdAt: '2024-06-01'
    } as TemuTemplate,
    {
        id: '2',
        productId: '1',
        platform: 'shein',
        templateName: 'Shein基础款T恤模板',
        shopName: 'Shein时尚店',
        productTitle: 'Basic Cotton T-Shirt Comfortable Breathable Multi-Color',
        category: 'Tops',
        brand: 'SHEIN',
        material: '100% Cotton',
        specifications: [
            {id: '1', size: 'S', color: 'White', price: 12.99, stock: 100},
            {id: '2', size: 'M', color: 'White', price: 12.99, stock: 150},
            {id: '3', size: 'L', color: 'Black', price: 12.99, stock: 120}
        ],
        createdAt: '2024-06-02'
    } as SheinTemplate
];

// 商品底版信息API
export const productBaseApi = {
    // 分页查询商品
    list: async (page: number = 1, pageSize: number = 10) => {
        const res = await fetch(`${BASE_URL}/pageQuery?page=${page}&perPage=${pageSize}`);
        const data = await res.json();
        if (data.code !== 'CA000000') throw new Error('查询失败');
        return {
            list: (data.page.content as any[]).map((item: any) => ({
                id: item.id?.toString(),
                name: item.name,
                type: item.type,
                category: '',
                supplier: '',
                createdAt: ''
            })),
            pagination: {
                page: data.page.page.number + 1,
                pageSize: data.page.page.size,
                total: data.page.page.totalElements,
            },
        };
    },

    // 新增商品
    add: async (data: Omit<ProductBase, 'id' | 'createdAt'>): Promise<any> => {
        const {createdAt, updatedAt, category, supplier, description, ...rest} = data as any;
        const res = await fetch(`${BASE_URL}/addOrUpdate`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(rest),
        });
        if (res.status !== 200) throw new Error('保存失败');
        return res;
    },

    // 更新商品
    update: async (id: string, data: Partial<ProductBase>): Promise<any> => {
        const {createdAt, updatedAt, category, supplier, description, ...rest} = data as any;
        const res = await fetch(`${BASE_URL}/addOrUpdate`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                id: parseInt(id),
                ...rest
            }),
        });
        if (res.status !== 200) throw new Error('保存失败');
        debugger
        return res.body;
    },

    // 根据ID获取商品
    getById: async (id: string): Promise<ProductBase | null> => {
        const res = await fetch(`${BASE_URL}/get/${id}`);
        if (res.status !== 200) {
            throw new Error('查询失败');
        }
        const data: any = res.body
        return {
            id: data.id?.toString(),
            name: data.name,
            type: data.type,
            category: '',
            supplier: '',
            createdAt: ''
        };
    },

    // 删除商品
    remove: async (id: string): Promise<boolean> => {
        const res = await fetch(`${BASE_URL}/delete/${id}`, {
            method: 'GET'
        });
        if (res.status !== 200) throw new Error('删除失败');
        return true;
    }
};

// 平台模板API
export const platformTemplateApi = {
    // 根据商品ID分页查询模板列表
    listByProductId: async (productId: string, page: number = 1, pageSize: number = 10): Promise<PageResponse<PlatformTemplate>> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        const productTemplates = mockPlatformTemplates.filter(t => t.productId === productId);
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const list = productTemplates.slice(start, end);

        return {
            list,
            pagination: {
                page,
                pageSize,
                total: productTemplates.length
            }
        };
    },

    // 根据ID获取模板
    getById: async (id: string): Promise<PlatformTemplate | null> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        return mockPlatformTemplates.find(t => t.id === id) || null;
    },

    // 根据商品ID和平台获取模板
    getByProductIdAndPlatform: async (productId: string, platform: string): Promise<PlatformTemplate | null> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        return mockPlatformTemplates.find(t => t.productId === productId && t.platform === platform) || null;
    },

    // 保存模板
    save: async (data: Omit<PlatformTemplate, 'id' | 'createdAt'>): Promise<PlatformTemplate> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        // 检查是否已存在相同ID的模板（更新模式）
        const existingIndex = mockPlatformTemplates.findIndex(t => t.id === (data as any).id);

        if (existingIndex >= 0) {
            // 更新现有模板
            mockPlatformTemplates[existingIndex] = {
                ...mockPlatformTemplates[existingIndex],
                ...data,
                updatedAt: new Date().toISOString().split('T')[0]
            } as PlatformTemplate;
            return mockPlatformTemplates[existingIndex];
        } else {
            // 创建新模板
            const newTemplate: PlatformTemplate = {
                ...data,
                id: Date.now().toString(),
                createdAt: new Date().toISOString().split('T')[0]
            } as PlatformTemplate;
            mockPlatformTemplates.push(newTemplate);
            return newTemplate;
        }
    },

    // 删除模板
    remove: async (id: string): Promise<boolean> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        const index = mockPlatformTemplates.findIndex(t => t.id === id);
        if (index === -1) return false;

        mockPlatformTemplates.splice(index, 1);
        return true;
    }
};

// 保持向后兼容的Temu模板API
export const temuTemplateApi = {
    // 根据商品ID获取模板
    getByProductId: async (productId: string): Promise<TemuTemplate | null> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        const template = mockPlatformTemplates.find(t => t.productId === productId && t.platform === 'temu') as TemuTemplate;
        return template || null;
    },

    // 保存模板
    save: async (data: Omit<TemuTemplate, 'id' | 'createdAt'>): Promise<TemuTemplate> => {
        return platformTemplateApi.save(data) as Promise<TemuTemplate>;
    },

    // 删除模板
    remove: async (productId: string): Promise<boolean> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        const template = mockPlatformTemplates.find(t => t.productId === productId && t.platform === 'temu');
        if (!template) return false;

        return platformTemplateApi.remove(template.id);
    }
};

// 商品属性API
export const goodsPropertiesApi = {
    // 获取商品属性模板
    getPropertyTemplates: async (): Promise<PropertyTemplate[]> => {
        await new Promise(resolve => setTimeout(resolve, 300));

        // 返回常用的商品属性模板
        return [
            {
                pid: 1,
                templateModuleId: 278182,
                templatePid: 1464648,
                refPid: 12,
                name: "Material",
                valueUnitList: null,
                values: [
                    { vid: 2, value: "Linen" },
                    { vid: 3, value: "Elastane" },
                    { vid: 4, value: "Cupro" },
                    { vid: 5, value: "Lyocell" },
                    { vid: 6, value: "Silk" },
                    { vid: 7, value: "Cotton" },
                    { vid: 8, value: "Polyester" },
                    { vid: 9, value: "Wool" },
                    { vid: 10, value: "Cashmere" }
                ],
                required: true,
                isSale: false,
                type: 'select'
            },
            {
                pid: 2,
                templateModuleId: 278182,
                templatePid: 1464647,
                refPid: 15,
                name: "Composition",
                valueUnitList: [{ valueUnit: "%", valueUnitId: 57 }],
                values: [
                    { vid: 35385, value: "Nylon" },
                    { vid: 35386, value: "Polyester" },
                    { vid: 35387, value: "Cotton" },
                    { vid: 35388, value: "Elastane" },
                    { vid: 35389, value: "Spandex" },
                    { vid: 35390, value: "Acrylic" },
                    { vid: 35391, value: "Silk" },
                    { vid: 35392, value: "Linen" },
                    { vid: 35393, value: "Modal" },
                    { vid: 35394, value: "Viscose" }
                ],
                required: true,
                isSale: false,
                type: 'select'
            },
            {
                pid: 12,
                templateModuleId: 278182,
                templatePid: 1464649,
                refPid: 28,
                name: "Length",
                valueUnitList: null,
                values: [
                    { vid: 290, value: "Short Length" },
                    { vid: 291, value: "Long length" },
                    { vid: 293, value: "Regular" }
                ],
                required: false,
                isSale: false,
                type: 'select'
            },
            {
                pid: 19,
                templateModuleId: 278182,
                templatePid: 1464650,
                refPid: 74,
                name: "Belt",
                valueUnitList: null,
                values: [
                    { vid: 550, value: "Yes" },
                    { vid: 551, value: "No" }
                ],
                required: false,
                isSale: false,
                type: 'select'
            },
            {
                pid: 16,
                templateModuleId: 278182,
                templatePid: 1464651,
                refPid: 29,
                name: "Sleeve Length",
                valueUnitList: null,
                values: [
                    { vid: 294, value: "Long Sleeve" },
                    { vid: 295, value: "Sleeveless" },
                    { vid: 296, value: "Short Sleeve" },
                    { vid: 297, value: "Cap Sleeve" },
                    { vid: 298, value: "Three Quarter Length Sleeve" },
                    { vid: 299, value: "Half Sleeve" }
                ],
                required: false,
                isSale: false,
                type: 'select'
            },
            {
                pid: 11,
                templateModuleId: 278182,
                templatePid: 1464652,
                refPid: 27,
                name: "Sleeve Type",
                valueUnitList: null,
                values: [
                    { vid: 275, value: "Raglan sleeve" },
                    { vid: 276, value: "Roll-up sleeve" },
                    { vid: 277, value: "Ruffle sleeve" },
                    { vid: 278, value: "Lantern Sleeve" },
                    { vid: 279, value: "Puff Sleeve" },
                    { vid: 280, value: "Regular Sleeve" },
                    { vid: 281, value: "Drop Shoulder" }
                ],
                required: false,
                isSale: false,
                type: 'select'
            },
            {
                pid: 3,
                templateModuleId: 278183,
                templatePid: 1464649,
                refPid: 20,
                name: "Weight",
                valueUnitList: [
                    { valueUnit: "g", valueUnitId: 58 },
                    { valueUnit: "kg", valueUnitId: 59 }
                ],
                values: [],
                required: false,
                isSale: false,
                type: 'input'
            },
            {
                pid: 4,
                templateModuleId: 278184,
                templatePid: 1464650,
                refPid: 25,
                name: "Brand",
                valueUnitList: null,
                values: [],
                required: false,
                isSale: false,
                type: 'input'
            }
        ];
    }
};
