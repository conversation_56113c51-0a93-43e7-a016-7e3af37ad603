import { createSignal, createEffect, For, Show } from 'solid-js';
import type { GoodsProperty, PropertyTemplate } from './types';
import { PlusIcon, TrashIcon } from '../../assets/Icons.jsx';

interface Props {
  goodsProperties: GoodsProperty[];
  onUpdate: (properties: GoodsProperty[]) => void;
}

// Mock商品属性模板数据
const mockPropertyTemplates: PropertyTemplate[] = [
  {
    pid: 1,
    templateModuleId: 278182,
    templatePid: 1464648,
    refPid: 12,
    name: "Material",
    nameEn: "材质",
    valueUnitList: null,
    values: [
      { vid: 2, value: "Linen", valueEn: "亚麻" },
      { vid: 3, value: "Elastane", valueEn: "氨纶" },
      { vid: 4, value: "Cotton", valueEn: "棉" },
      { vid: 5, value: "Polyester", valueEn: "聚酯纤维" },
      { vid: 6, value: "Silk", valueEn: "丝绸" },
      { vid: 7, value: "Wool", valueEn: "羊毛" }
    ],
    required: true,
    isSale: false,
    type: 'select'
  },
  {
    pid: 2,
    templateModuleId: 278182,
    templatePid: 1464647,
    refPid: 15,
    name: "Composition",
    nameEn: "成分",
    valueUnitList: [
      { valueUnit: "%", valueUnitId: 57 }
    ],
    values: [
      { vid: 35385, value: "Nylon", valueEn: "尼龙" },
      { vid: 35386, value: "Polyester", valueEn: "聚酯纤维" },
      { vid: 35387, value: "Cotton", valueEn: "棉" },
      { vid: 35388, value: "Elastane", valueEn: "氨纶" },
      { vid: 35389, value: "Spandex", valueEn: "氨纶" }
    ],
    required: false,
    isSale: false,
    type: 'select'
  },
  {
    pid: 3,
    templateModuleId: 278183,
    templatePid: 1464649,
    refPid: 20,
    name: "Weight",
    nameEn: "重量",
    valueUnitList: [
      { valueUnit: "g", valueUnitId: 58 },
      { valueUnit: "kg", valueUnitId: 59 }
    ],
    values: [],
    required: false,
    isSale: false,
    type: 'number'
  },
  {
    pid: 4,
    templateModuleId: 278184,
    templatePid: 1464650,
    refPid: 25,
    name: "Brand",
    nameEn: "品牌",
    valueUnitList: null,
    values: [],
    required: false,
    isSale: false,
    type: 'input'
  }
];

export default function GoodsPropertiesConfig(props: Props) {
  const [selectedTemplates, setSelectedTemplates] = createSignal<PropertyTemplate[]>([]);

  // 初始化已选择的模板
  createEffect(() => {
    const existingTemplates = props.goodsProperties.map(prop => {
      const template = mockPropertyTemplates.find(t => t.refPid === prop.refPid);
      return template;
    }).filter(Boolean) as PropertyTemplate[];
    setSelectedTemplates(existingTemplates);
  });

  // 添加属性
  function addProperty(template: PropertyTemplate) {
    const newProperty: GoodsProperty = {
      templatePid: template.templatePid,
      refPid: template.refPid,
      vid: null,
      specId: null,
      imgUrl: null,
      note: null,
      parentSpecId: null,
      valueUnitId: template.valueUnitList?.[0]?.valueUnitId || null,
      groupId: null,
      valueUnit: template.valueUnitList?.[0]?.valueUnit || null,
      numberInputValue: null,
      value: null,
      name: template.name,
      nameEn: template.nameEn,
      required: template.required,
      type: template.type,
      options: template.values.map(v => ({ vid: v.vid, value: v.value, valueEn: v.valueEn }))
    };

    const updatedProperties = [...props.goodsProperties, newProperty];
    props.onUpdate(updatedProperties);
    
    setSelectedTemplates(prev => [...prev, template]);
  }

  // 删除属性
  function removeProperty(index: number) {
    const property = props.goodsProperties[index];
    const updatedProperties = props.goodsProperties.filter((_, i) => i !== index);
    props.onUpdate(updatedProperties);
    
    setSelectedTemplates(prev => prev.filter(t => t.refPid !== property.refPid));
  }

  // 更新属性值
  function updateProperty(index: number, field: keyof GoodsProperty, value: any) {
    const updatedProperties = [...props.goodsProperties];
    updatedProperties[index] = { ...updatedProperties[index], [field]: value };
    
    // 如果是选择类型且选择了值，同时更新vid
    if (field === 'value' && updatedProperties[index].type === 'select') {
      const option = updatedProperties[index].options?.find(opt => opt.value === value);
      if (option) {
        updatedProperties[index].vid = option.vid;
      }
    }
    
    props.onUpdate(updatedProperties);
  }

  // 获取可添加的模板（排除已选择的）
  function getAvailableTemplates() {
    const selectedRefPids = selectedTemplates().map(t => t.refPid);
    return mockPropertyTemplates.filter(t => !selectedRefPids.includes(t.refPid));
  }

  return (
    <div class="space-y-4">
      {/* 已添加的属性列表 */}
      <Show when={props.goodsProperties.length > 0} fallback={
        <div class="text-center py-8 text-base-content/70">
          <div class="text-sm">暂无商品属性，请从下方选择添加</div>
        </div>
      }>
        <div class="space-y-3">
          <For each={props.goodsProperties}>
            {(property, index) => (
              <div class="card bg-base-100 border border-base-300">
                <div class="card-body p-4">
                  <div class="flex justify-between items-start mb-3">
                    <div class="flex items-center gap-2">
                      <h4 class="font-medium">
                        {property.nameEn} / {property.name}
                        {property.required && <span class="text-error">*</span>}
                      </h4>
                    </div>
                    <button 
                      class="btn btn-ghost btn-xs text-error"
                      onClick={() => removeProperty(index())}
                    >
                      <TrashIcon />
                    </button>
                  </div>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {/* 属性值输入 */}
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text text-xs">属性值</span>
                      </label>
                      <Show when={property.type === 'select'} fallback={
                        <div class="flex gap-2">
                          <input 
                            type={property.type === 'number' ? 'number' : 'text'}
                            class="input input-bordered input-sm flex-1" 
                            value={property.numberInputValue || property.value || ''}
                            onInput={e => updateProperty(index(), 
                              property.type === 'number' ? 'numberInputValue' : 'value', 
                              e.currentTarget.value
                            )}
                            placeholder={`请输入${property.nameEn}`}
                          />
                          <Show when={property.valueUnit}>
                            <span class="flex items-center px-2 text-sm text-base-content/70">
                              {property.valueUnit}
                            </span>
                          </Show>
                        </div>
                      }>
                        <select 
                          class="select select-bordered select-sm"
                          value={property.value || ''}
                          onChange={e => updateProperty(index(), 'value', e.currentTarget.value)}
                        >
                          <option value="">请选择{property.nameEn}</option>
                          <For each={property.options || []}>
                            {option => (
                              <option value={option.value}>
                                {option.valueEn} / {option.value}
                              </option>
                            )}
                          </For>
                        </select>
                      </Show>
                    </div>
                    
                    {/* 备注 */}
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text text-xs">备注</span>
                      </label>
                      <input 
                        type="text"
                        class="input input-bordered input-sm" 
                        value={property.note || ''}
                        onInput={e => updateProperty(index(), 'note', e.currentTarget.value)}
                        placeholder="可选备注信息"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </For>
        </div>
      </Show>

      {/* 添加属性按钮 */}
      <Show when={getAvailableTemplates().length > 0}>
        <div class="card bg-base-100 border border-dashed border-base-300">
          <div class="card-body p-4">
            <h4 class="font-medium mb-3">添加商品属性</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              <For each={getAvailableTemplates()}>
                {template => (
                  <button 
                    class="btn btn-outline btn-sm justify-start"
                    onClick={() => addProperty(template)}
                  >
                    <PlusIcon />
                    <span class="truncate">
                      {template.nameEn} / {template.name}
                      {template.required && <span class="text-error ml-1">*</span>}
                    </span>
                  </button>
                )}
              </For>
            </div>
          </div>
        </div>
      </Show>
    </div>
  );
}
