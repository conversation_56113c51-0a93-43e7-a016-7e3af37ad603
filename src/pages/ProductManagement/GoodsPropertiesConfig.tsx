import { createSignal, createEffect, createResource, For, Show } from 'solid-js';
import type { GoodsProperty, PropertyTemplate } from './types';
import { goodsPropertiesApi } from './api';
import { getPropertyNameChinese, getPropertyValueChinese } from './propertyTranslations';

interface Props {
  goodsProperties: GoodsProperty[];
  onUpdate: (properties: GoodsProperty[]) => void;
}

export default function GoodsPropertiesConfig(props: Props) {
  // 加载商品属性模板
  const [propertyTemplates] = createResource(goodsPropertiesApi.getPropertyTemplates);

  // 获取属性的中文名称
  function getChineseName(englishName: string): string {
    return propertyNameMap[englishName] || englishName;
  }

  // 更新属性值
  function updatePropertyValue(template: PropertyTemplate, value: string) {
    const existingIndex = props.goodsProperties.findIndex(p => p.refPid === template.refPid);

    if (existingIndex >= 0) {
      // 更新现有属性
      const updatedProperties = [...props.goodsProperties];
      const selectedOption = template.values.find(v => v.value === value);

      updatedProperties[existingIndex] = {
        ...updatedProperties[existingIndex],
        value: value || null,
        vid: selectedOption?.vid || null,
        numberInputValue: template.type === 'input' ? value : null
      };

      props.onUpdate(updatedProperties);
    } else if (value) {
      // 添加新属性
      const selectedOption = template.values.find(v => v.value === value);
      const newProperty: GoodsProperty = {
        templatePid: template.templatePid,
        refPid: template.refPid,
        vid: selectedOption?.vid || null,
        specId: null,
        imgUrl: null,
        note: null,
        parentSpecId: null,
        valueUnitId: template.valueUnitList?.[0]?.valueUnitId || null,
        groupId: null,
        valueUnit: template.valueUnitList?.[0]?.valueUnit || null,
        numberInputValue: template.type === 'input' ? value : null,
        value: value
      };

      props.onUpdate([...props.goodsProperties, newProperty]);
    }
  }

  // 获取属性的当前值
  function getPropertyValue(template: PropertyTemplate): string {
    const property = props.goodsProperties.find(p => p.refPid === template.refPid);
    return property?.value || property?.numberInputValue || '';
  }

  return (
    <div class="space-y-4">
      <Show when={propertyTemplates.loading}>
        <div class="text-center py-8">
          <span class="loading loading-spinner loading-md"></span>
          <p class="text-sm text-base-content/70 mt-2">正在加载商品属性...</p>
        </div>
      </Show>

      <Show when={propertyTemplates()}>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <For each={propertyTemplates() || []}>
            {template => (
              <div class="form-control">
                <label class="label">
                  <span class="label-text">
                    {getChineseName(template.name)}
                    <span class="text-base-content/50 ml-2">{template.name}</span>
                    {template.required && <span class="text-error ml-1">*</span>}
                  </span>
                </label>

                <Show when={template.values && template.values.length > 0} fallback={
                  <div class="flex gap-2">
                    <input
                      type="text"
                      class="input input-bordered input-sm flex-1"
                      value={getPropertyValue(template)}
                      onInput={e => updatePropertyValue(template, e.currentTarget.value)}
                      placeholder={`请输入${getChineseName(template.name)}`}
                    />
                    <Show when={template.valueUnitList && template.valueUnitList.length > 0}>
                      <span class="flex items-center px-2 text-sm text-base-content/70">
                        {template.valueUnitList[0].valueUnit}
                      </span>
                    </Show>
                  </div>
                }>
                  <select
                    class="select select-bordered select-sm"
                    value={getPropertyValue(template)}
                    onChange={e => updatePropertyValue(template, e.currentTarget.value)}
                  >
                    <option value="">请选择{getChineseName(template.name)}</option>
                    <For each={template.values}>
                      {option => (
                        <option value={option.value}>
                          {option.value}
                        </option>
                      )}
                    </For>
                  </select>
                </Show>
              </div>
            )}
          </For>
        </div>
      </Show>
    </div>
  );
}
