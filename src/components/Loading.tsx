import { Component } from 'solid-js';
import './Loading.css';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export const Loading: Component<LoadingProps> = (props) => {
  const size = () => props.size || 'md';
  const text = () => props.text || '加载中...';
  
  const sizeClasses = () => {
    switch (size()) {
      case 'sm': return 'w-6 h-6';
      case 'lg': return 'w-12 h-12';
      default: return 'w-8 h-8';
    }
  };

  return (
    <div class={`flex flex-col items-center justify-center py-8 ${props.className || ''}`}>
      {/* 旋转的圆环 */}
      <div class={`${sizeClasses()} relative`}>
        <div class="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
        <div class="absolute inset-0 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
      
      {/* 加载文本 */}
      <div class="mt-3 text-sm text-gray-600 animate-pulse">
        {text()}
      </div>
    </div>
  );
};

export const TableLoading: Component<{ columns?: number }> = (props) => {
  const columns = () => props.columns || 6;
  
  return (
    <div class="animate-pulse">
      {/* 表头骨架 */}
      <div class="flex bg-gray-100 rounded-t-lg p-4 mb-2">
        {Array.from({ length: columns() }).map((_, index) => (
          <div class="flex-1 h-4 bg-gray-300 rounded mr-4 last:mr-0"></div>
        ))}
      </div>
      
      {/* 表格行骨架 */}
      {Array.from({ length: 5 }).map((_, rowIndex) => (
        <div class="flex bg-white border-b p-4">
          {Array.from({ length: columns() }).map((_, colIndex) => (
            <div class="flex-1 mr-4 last:mr-0">
              <div class={`h-4 bg-gray-200 rounded ${colIndex === 0 ? 'w-3/4' : 'w-full'}`}></div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export const SpinnerLoading: Component<LoadingProps> = (props) => {
  const size = () => props.size || 'md';
  
  const sizeClasses = () => {
    switch (size()) {
      case 'sm': return 'w-4 h-4';
      case 'lg': return 'w-10 h-10';
      default: return 'w-6 h-6';
    }
  };

  return (
    <div class={`inline-flex items-center ${props.className || ''}`}>
      <div class={`${sizeClasses()} border-2 border-primary border-t-transparent rounded-full animate-spin`}></div>
      {props.text && (
        <span class="ml-2 text-sm text-gray-600">{props.text}</span>
      )}
    </div>
  );
};

// 商品卡片加载骨架
export const ProductCardLoading: Component = () => {
  return (
    <div class="animate-pulse bg-white rounded-lg shadow-sm border p-4">
      <div class="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
      <div class="space-y-2">
        <div class="h-3 bg-gray-200 rounded w-1/2"></div>
        <div class="h-3 bg-gray-200 rounded w-2/3"></div>
        <div class="h-3 bg-gray-200 rounded w-1/3"></div>
      </div>
      <div class="flex justify-end mt-4 space-x-2">
        <div class="h-6 bg-gray-200 rounded w-12"></div>
        <div class="h-6 bg-gray-200 rounded w-16"></div>
        <div class="h-6 bg-gray-200 rounded w-12"></div>
      </div>
    </div>
  );
};

// 页面级加载组件
export const PageLoading: Component<{ text?: string }> = (props) => {
  return (
    <div class="min-h-[50vh] flex flex-col items-center justify-center">
      <div class="relative">
        {/* 外圈 */}
        <div class="w-16 h-16 border-4 border-gray-200 rounded-full"></div>
        {/* 内圈旋转 */}
        <div class="absolute top-0 left-0 w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        {/* 中心点 */}
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-primary rounded-full animate-pulse"></div>
      </div>

      <div class="mt-6 text-center">
        <div class="text-lg font-medium text-gray-700 mb-2">
          {props.text || '正在加载数据...'}
        </div>
        <div class="text-sm text-gray-500 animate-pulse">
          请稍候片刻
        </div>
      </div>
    </div>
  );
};

// 特殊的商品加载动画
export const ProductTableLoading: Component = () => {
  return (
    <div class="w-full">
      {/* 表头 */}
      <div class="bg-base-200 rounded-t-lg p-4 mb-1">
        <div class="flex space-x-4">
          <div class="flex-1 h-4 bg-base-300 rounded shimmer"></div>
          <div class="flex-1 h-4 bg-base-300 rounded shimmer"></div>
          <div class="flex-1 h-4 bg-base-300 rounded shimmer"></div>
          <div class="flex-1 h-4 bg-base-300 rounded shimmer"></div>
          <div class="flex-1 h-4 bg-base-300 rounded shimmer"></div>
          <div class="w-32 h-4 bg-base-300 rounded shimmer"></div>
        </div>
      </div>

      {/* 表格行 */}
      {Array.from({ length: 5 }).map((_, index) => (
        <div class="bg-white border-b border-base-200 p-4" style={`animation-delay: ${index * 100}ms`}>
          <div class="flex space-x-4 items-center">
            <div class="flex-1">
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-1 shimmer"></div>
              <div class="h-3 bg-gray-100 rounded w-1/2 shimmer"></div>
            </div>
            <div class="flex-1 h-3 bg-gray-200 rounded shimmer"></div>
            <div class="flex-1 h-3 bg-gray-200 rounded shimmer"></div>
            <div class="flex-1 h-3 bg-gray-200 rounded shimmer"></div>
            <div class="flex-1 h-3 bg-gray-200 rounded shimmer"></div>
            <div class="w-32 flex space-x-1">
              <div class="h-6 bg-blue-200 rounded w-12 shimmer"></div>
              <div class="h-6 bg-green-200 rounded w-16 shimmer"></div>
              <div class="h-6 bg-red-200 rounded w-12 shimmer"></div>
            </div>
          </div>
        </div>
      ))}

      {/* 加载指示器 */}
      <div class="flex justify-center items-center py-8">
        <div class="wave-loading text-primary">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <span class="ml-4 text-sm text-gray-600 loading-text">正在加载商品数据...</span>
      </div>
    </div>
  );
};

// 波浪式加载组件
export const WaveLoading: Component<{ text?: string; className?: string }> = (props) => {
  return (
    <div class={`flex items-center justify-center ${props.className || ''}`}>
      <div class="wave-loading text-primary">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
      {props.text && (
        <span class="ml-3 text-sm text-gray-600">{props.text}</span>
      )}
    </div>
  );
};

// 弹跳球加载组件
export const BounceLoading: Component<{ text?: string; className?: string }> = (props) => {
  return (
    <div class={`flex items-center justify-center ${props.className || ''}`}>
      <div class="bounce-loading text-primary">
        <div class="ball"></div>
        <div class="ball"></div>
        <div class="ball"></div>
      </div>
      {props.text && (
        <span class="ml-3 text-sm text-gray-600">{props.text}</span>
      )}
    </div>
  );
};
