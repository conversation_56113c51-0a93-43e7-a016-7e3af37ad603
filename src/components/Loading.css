/* 自定义Loading动画 */

/* 波浪式加载动画 */
@keyframes wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-15px);
  }
}

.wave-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.wave-loading .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  animation: wave 1.4s ease-in-out infinite both;
}

.wave-loading .dot:nth-child(1) { animation-delay: -0.32s; }
.wave-loading .dot:nth-child(2) { animation-delay: -0.16s; }
.wave-loading .dot:nth-child(3) { animation-delay: 0s; }

/* 脉冲圆环动画 */
@keyframes pulse-ring {
  0% {
    transform: scale(0.33);
  }
  40%, 50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.33);
  }
}

.pulse-ring {
  position: relative;
  display: inline-block;
}

.pulse-ring::before,
.pulse-ring::after {
  content: '';
  position: absolute;
  border: 2px solid currentColor;
  border-radius: 50%;
  opacity: 1;
  animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.pulse-ring::before {
  animation-delay: -0.625s;
}

/* 旋转方块动画 */
@keyframes rotate-square {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}

.rotating-square {
  width: 40px;
  height: 40px;
  background-color: currentColor;
  animation: rotate-square 1.2s infinite ease-in-out;
}

/* 弹跳球动画 */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.bounce-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.bounce-loading .ball {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: currentColor;
  animation: bounce 1.4s ease-in-out infinite both;
}

.bounce-loading .ball:nth-child(1) { animation-delay: -0.32s; }
.bounce-loading .ball:nth-child(2) { animation-delay: -0.16s; }
.bounce-loading .ball:nth-child(3) { animation-delay: 0s; }

/* 渐变条纹动画 */
@keyframes stripe-move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 0;
  }
}

.stripe-loading {
  width: 100%;
  height: 4px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.5),
    transparent
  );
  background-size: 40px 100%;
  animation: stripe-move 1s linear infinite;
  border-radius: 2px;
}

/* 表格行加载动画 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 圆形进度条 */
@keyframes circle-progress {
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 0;
  }
}

.circle-progress {
  transform: rotate(-90deg);
}

.circle-progress circle {
  stroke-dasharray: 0 100;
  animation: circle-progress 2s ease-in-out infinite;
}

/* 加载文字动画 */
@keyframes text-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

.loading-text {
  animation: text-blink 1.5s ease-in-out infinite;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .wave-loading .dot {
    width: 6px;
    height: 6px;
  }
  
  .rotating-square {
    width: 30px;
    height: 30px;
  }
  
  .bounce-loading .ball {
    width: 10px;
    height: 10px;
  }
}
