{"templateId": 139091, "goodsProperties": [{"pid": 2, "templateModuleId": 278182, "templatePid": 1464647, "refPid": 15, "name": "Composition", "valueUnitList": [{"valueUnit": "%", "valueUnitId": 57}], "values": [{"vid": 35385, "value": "Nylon"}, {"vid": 35386, "value": "Polyester"}, {"vid": 35387, "value": "Polyamide"}, {"vid": 35388, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 35389, "value": "Spandex"}, {"vid": 35390, "value": "Acrylic"}, {"vid": 35391, "value": "Cotton"}, {"vid": 35392, "value": "Silk"}, {"vid": 35393, "value": "Linen"}, {"vid": 35394, "value": "Modal"}, {"vid": 35395, "value": "Viscose"}, {"vid": 35396, "value": "Rayon"}, {"vid": 35397, "value": "Carbon"}, {"vid": 35398, "value": "Metal"}, {"vid": 35399, "value": "Wool"}, {"vid": 35400, "value": "Cashmere"}, {"vid": 35401, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 35403, "value": "<PERSON> Hair"}, {"vid": 35404, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 35405, "value": "<PERSON><PERSON>"}, {"vid": 35406, "value": "<PERSON> Leather"}, {"vid": 35407, "value": "Polyurethane"}, {"vid": 35408, "value": "Polyvinyl Chloride"}, {"vid": 35409, "value": "Cupro"}, {"vid": 35410, "value": "Lyocell"}, {"vid": 35411, "value": "Acetate"}, {"vid": 35412, "value": "Down"}, {"vid": 35413, "value": "<PERSON><PERSON>"}, {"vid": 35414, "value": "Duck Down"}, {"vid": 35415, "value": "Alpaca"}, {"vid": 35364, "value": "Metallized Fibres"}, {"vid": 47217, "value": "Polypropylene fibers"}, {"vid": 36254, "value": "Other Fibers"}, {"vid": 121, "value": "<PERSON><PERSON>"}, {"vid": 98, "value": "Polyester"}, {"vid": 214835, "value": "Triacetate"}], "required": true, "isSale": false}, {"pid": 1, "templateModuleId": 278182, "templatePid": 1464648, "refPid": 12, "name": "Material", "valueUnitList": null, "values": [{"vid": 2, "value": "Linen"}, {"vid": 3, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 4, "value": "Cupro"}, {"vid": 5, "value": "Lyocell"}, {"vid": 6, "value": "Silk"}, {"vid": 7, "value": "<PERSON><PERSON>"}, {"vid": 11, "value": "Cuprammonium"}, {"vid": 12, "value": "Bamboo Fiber"}, {"vid": 15, "value": "Organic Cotton Blends"}, {"vid": 16, "value": "Organic Cotton"}, {"vid": 17, "value": "Recycled cotton blend"}, {"vid": 18, "value": "Recycled Cotton"}, {"vid": 19, "value": "Recycled Polyester Blends"}, {"vid": 20, "value": "Acetate"}, {"vid": 21, "value": "Faux leather"}, {"vid": 22, "value": "Mesh embroidery"}, {"vid": 23, "value": "Jersey"}, {"vid": 24, "value": "Tencel"}, {"vid": 25, "value": "Guipure Lace"}, {"vid": 26, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 27, "value": "C<PERSON>chet"}, {"vid": 28, "value": "Glitter"}, {"vid": 29, "value": "Modal"}, {"vid": 30, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 31, "value": "Spandex"}, {"vid": 32, "value": "Lycra (spandex)"}, {"vid": 33, "value": "Cotton Blend"}, {"vid": 34, "value": "PU Leather"}, {"vid": 35, "value": "Rayon"}, {"vid": 36, "value": "Satin"}, {"vid": 37, "value": "<PERSON><PERSON>"}, {"vid": 38, "value": "Tweed"}, {"vid": 39, "value": "Faux Fur"}, {"vid": 40, "value": "Lace"}, {"vid": 41, "value": "<PERSON><PERSON>"}, {"vid": 42, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 43, "value": "Sequins"}, {"vid": 44, "value": "Corduroy"}, {"vid": 45, "value": "<PERSON><PERSON>"}, {"vid": 46, "value": "Fleece"}, {"vid": 47, "value": "Acrylic"}, {"vid": 48, "value": "<PERSON>ffon"}, {"vid": 49, "value": "Cotton"}, {"vid": 50, "value": "Flannel"}, {"vid": 51, "value": "Metal"}, {"vid": 52, "value": "Nylon"}, {"vid": 53, "value": "Organza"}, {"vid": 54, "value": "Polyamide"}, {"vid": 55, "value": "Polyester"}, {"vid": 56, "value": "Silicone"}, {"vid": 57, "value": "Velo<PERSON>"}, {"vid": 58, "value": "Viscose"}, {"vid": 64, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 65, "value": "Polyethylene terephthalate"}, {"vid": 66, "value": "Aluminum Alloy"}, {"vid": 67, "value": "<PERSON> Fleece"}, {"vid": 68, "value": "PVC"}, {"vid": 70, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 73, "value": "Recycled Polyester"}, {"vid": 1156, "value": "PVC"}, {"vid": 1157, "value": "Polyester And Spandex"}, {"vid": 969, "value": "PU"}, {"vid": 61943, "value": "Metal Coated Fiber"}, {"vid": 52829, "value": "fox fur"}, {"vid": 60052, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 59, "value": "Wool"}], "required": true, "isSale": false}, {"pid": 12, "templateModuleId": 278182, "templatePid": 1464649, "refPid": 28, "name": "Length", "valueUnitList": null, "values": [{"vid": 290, "value": "Short Length"}, {"vid": 291, "value": "Long length"}, {"vid": 293, "value": "Regular"}], "required": false, "isSale": false}, {"pid": 19, "templateModuleId": 278182, "templatePid": 1464650, "refPid": 74, "name": "Belt", "valueUnitList": null, "values": [{"vid": 550, "value": "Yes"}, {"vid": 551, "value": "No"}], "required": false, "isSale": false}, {"pid": 16, "templateModuleId": 278182, "templatePid": 1464651, "refPid": 29, "name": "Sleeve <PERSON>", "valueUnitList": null, "values": [{"vid": 294, "value": "Long Sleeve"}, {"vid": 295, "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"vid": 296, "value": "<PERSON>ve"}, {"vid": 297, "value": "Cap Sleeve"}, {"vid": 298, "value": "Three Quarter Length Sleeve"}, {"vid": 299, "value": "Half Sleeve"}], "required": false, "isSale": false}, {"pid": 11, "templateModuleId": 278182, "templatePid": 1464652, "refPid": 27, "name": "Sleeve Type", "valueUnitList": null, "values": [{"vid": 275, "value": "Raglan sleeve"}, {"vid": 276, "value": "Roll-up sleeve"}, {"vid": 277, "value": "Ruffle sleeve"}, {"vid": 278, "value": "Lantern Sleeve"}, {"vid": 279, "value": "<PERSON><PERSON>"}, {"vid": 280, "value": "Layered Sleeve"}, {"vid": 281, "value": "Split sleeve"}, {"vid": 282, "value": "Batwing sleeve"}, {"vid": 283, "value": "<PERSON>leeve"}, {"vid": 285, "value": "Cape sleeve"}, {"vid": 286, "value": "Leg of mutton sleeve"}, {"vid": 287, "value": "Petal Sleeve"}, {"vid": 288, "value": "Regular Sleeve"}, {"vid": 289, "value": "Drop Shoulder"}, {"vid": 1062, "value": "<PERSON><PERSON>"}], "required": false, "isSale": false}, {"pid": 50, "templateModuleId": 278182, "templatePid": 1464653, "refPid": 94, "name": "Chest Pad", "valueUnitList": null, "values": [{"vid": 1126, "value": "No"}, {"vid": 1127, "value": "No Padding"}, {"vid": 1129, "value": "Non Removable Padding"}, {"vid": 1130, "value": "Removable Padding"}], "required": false, "isSale": false}, {"pid": 21, "templateModuleId": 278182, "templatePid": 1464654, "refPid": 83, "name": "Details", "valueUnitList": null, "values": [{"vid": 549, "value": "Fake <PERSON>"}, {"vid": 552, "value": "Fake Pockets"}, {"vid": 553, "value": "Fake Drawstring"}, {"vid": 554, "value": "Applique"}, {"vid": 555, "value": "Asymmetrical"}, {"vid": 556, "value": "Beaded"}, {"vid": 557, "value": "Belt"}, {"vid": 558, "value": "Bow"}, {"vid": 559, "value": "<PERSON><PERSON>"}, {"vid": 560, "value": "Chain"}, {"vid": 561, "value": "Contrast Faux Fur"}, {"vid": 562, "value": "Color Block"}, {"vid": 563, "value": "Contrast Lace"}, {"vid": 564, "value": "Contrast Mesh"}, {"vid": 565, "value": "Contrast Sequin"}, {"vid": 566, "value": "High-waisted"}, {"vid": 567, "value": "Cross Strape"}, {"vid": 568, "value": "Hollow"}, {"vid": 569, "value": "Double Button"}, {"vid": 570, "value": "Draped"}, {"vid": 571, "value": "Drawstring"}, {"vid": 572, "value": "Embroidered"}, {"vid": 573, "value": "<PERSON><PERSON>"}, {"vid": 575, "value": "<PERSON><PERSON>"}, {"vid": 576, "value": "Tassel"}, {"vid": 577, "value": "Dipped Hem"}, {"vid": 578, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 579, "value": "Lace up"}, {"vid": 580, "value": "Let<PERSON><PERSON> Tri<PERSON>"}, {"vid": 581, "value": "Patched"}, {"vid": 583, "value": "Pearl"}, {"vid": 584, "value": "Pleated"}, {"vid": 585, "value": "<PERSON><PERSON>"}, {"vid": 586, "value": "Pocket"}, {"vid": 587, "value": "Raw Hem"}, {"vid": 588, "value": "Rhinestone"}, {"vid": 589, "value": "Ribbon"}, {"vid": 590, "value": "Ring"}, {"vid": 591, "value": "Ripped"}, {"vid": 592, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 593, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 594, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 595, "value": "Scallop"}, {"vid": 596, "value": "Sequins"}, {"vid": 597, "value": "<PERSON><PERSON>"}, {"vid": 599, "value": "Split"}, {"vid": 601, "value": "<PERSON><PERSON>"}, {"vid": 602, "value": "Tiered"}, {"vid": 603, "value": "Twist"}, {"vid": 604, "value": "Cross"}, {"vid": 605, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 607, "value": "Grom<PERSON>s"}, {"vid": 608, "value": "Rib-K<PERSON>t"}, {"vid": 609, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 610, "value": "Side Stripe"}, {"vid": 611, "value": "Contrast Binding"}, {"vid": 613, "value": "Shirred"}, {"vid": 614, "value": "Exaggerated R<PERSON>le"}, {"vid": 615, "value": "Big Bow"}, {"vid": 616, "value": "Front bow"}, {"vid": 617, "value": "Back bow"}, {"vid": 618, "value": "<PERSON>ie Front"}, {"vid": 619, "value": "Button Front"}, {"vid": 621, "value": "Eyelet Embroidery"}, {"vid": 832, "value": "<PERSON><PERSON>"}, {"vid": 834, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 964, "value": "Backless"}, {"vid": 999, "value": "Contrast Collar"}, {"vid": 1039, "value": "<PERSON><PERSON><PERSON> Bus<PERSON>"}, {"vid": 29210, "value": "None"}], "required": true, "isSale": false}, {"pid": 10, "templateModuleId": 278182, "templatePid": 1464655, "refPid": 26, "name": "Pattern", "valueUnitList": null, "values": [{"vid": 32883, "value": "Mushroom"}, {"vid": 32884, "value": "Anime"}, {"vid": 32885, "value": "Butterfly"}, {"vid": 215, "value": "Solid color"}, {"vid": 216, "value": "Geometric-pattern"}, {"vid": 217, "value": "Alphabets"}, {"vid": 218, "value": "Stripes"}, {"vid": 219, "value": "<PERSON><PERSON>"}, {"vid": 220, "value": "Portrait"}, {"vid": 221, "value": "Animals"}, {"vid": 222, "value": "Plaid"}, {"vid": 223, "value": "Gingham"}, {"vid": 224, "value": "<PERSON><PERSON>"}, {"vid": 225, "value": "Tropical"}, {"vid": 226, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 227, "value": "Polka dots"}, {"vid": 228, "value": "Patchwork"}, {"vid": 229, "value": "Car"}, {"vid": 230, "value": "Cartoon"}, {"vid": 231, "value": "Butterfly"}, {"vid": 233, "value": "Dinosaurs"}, {"vid": 234, "value": "Chevron"}, {"vid": 235, "value": "Christmas"}, {"vid": 236, "value": "Color Block"}, {"vid": 238, "value": "Crocodile"}, {"vid": 241, "value": "<PERSON><PERSON>"}, {"vid": 242, "value": "Galaxy"}, {"vid": 243, "value": "Drawing"}, {"vid": 244, "value": "Halloween"}, {"vid": 245, "value": "Heart"}, {"vid": 246, "value": "Houndstooth"}, {"vid": 247, "value": "<PERSON><PERSON>"}, {"vid": 248, "value": "Ombre"}, {"vid": 251, "value": "Snakeskin"}, {"vid": 252, "value": "Tribal"}, {"vid": 253, "value": "Fruit&Vegetable"}, {"vid": 254, "value": "Camo"}, {"vid": 255, "value": "Plants"}, {"vid": 256, "value": "Zebra Print"}, {"vid": 257, "value": "Crocodile"}, {"vid": 258, "value": "Fish Scales"}, {"vid": 259, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 260, "value": "Chain"}, {"vid": 261, "value": "Scarf Print"}, {"vid": 262, "value": "Di<PERSON><PERSON>"}, {"vid": 263, "value": "Tartan"}, {"vid": 264, "value": "Landscape"}, {"vid": 265, "value": "Rainbow Stripe"}, {"vid": 266, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 267, "value": "Flag"}, {"vid": 268, "value": "All Over Print"}, {"vid": 269, "value": "Pop art"}, {"vid": 270, "value": "Baroque"}, {"vid": 271, "value": "Marble"}, {"vid": 272, "value": "Fire"}, {"vid": 273, "value": "Map"}, {"vid": 898, "value": "Argyle"}, {"vid": 72882, "value": "Sun & Moon"}, {"vid": 72962, "value": "Attitude"}, {"vid": 72963, "value": "Role"}, {"vid": 72964, "value": "Faith"}, {"vid": 72965, "value": "Location"}, {"vid": 26633, "value": "Landscapes"}, {"vid": 73061, "value": "Western"}, {"vid": 60539, "value": "Food"}, {"vid": 26629, "value": "Architectural Pattern"}], "required": true, "isSale": false}, {"pid": 7, "templateModuleId": 278182, "templatePid": 1464656, "refPid": 24, "name": "<PERSON><PERSON>", "valueUnitList": null, "values": [{"vid": 209, "value": "Semi-Sheer"}, {"vid": 210, "value": "No"}, {"vid": 211, "value": "Yes"}], "required": true, "isSale": false}, {"pid": 6, "templateModuleId": 278182, "templatePid": 1464657, "refPid": 22, "name": "<PERSON><PERSON><PERSON>", "valueUnitList": null, "values": [{"vid": 205, "value": "Medium Stretch"}, {"vid": 206, "value": "Non-Stretch"}, {"vid": 207, "value": "Slight Stretch"}, {"vid": 208, "value": "High Stretch"}], "required": false, "isSale": false}, {"pid": 39, "templateModuleId": 278182, "templatePid": 1464658, "refPid": 86, "name": "Placket Type", "valueUnitList": null, "values": [{"vid": 873, "value": "Pullovers"}, {"vid": 874, "value": "Half Placket"}, {"vid": 876, "value": "Placket"}], "required": false, "isSale": false}, {"pid": 20, "templateModuleId": 278182, "templatePid": 1464659, "refPid": 113, "name": "<PERSON>tour", "valueUnitList": null, "values": [{"vid": 35191, "value": "H"}, {"vid": 35192, "value": "A"}, {"vid": 35193, "value": "T"}, {"vid": 35194, "value": "O"}, {"vid": 35195, "value": "X"}], "required": false, "isSale": false}, {"pid": 24, "templateModuleId": 278182, "templatePid": 1464660, "refPid": 76, "name": "Season", "valueUnitList": null, "values": [{"vid": 639, "value": "Fall"}, {"vid": 640, "value": "Spring"}, {"vid": 641, "value": "Spring/Fall"}, {"vid": 642, "value": "Summer"}, {"vid": 643, "value": "Winter"}, {"vid": 645, "value": "All"}, {"vid": 646, "value": "Spring/summer"}, {"vid": 647, "value": "Fall/Winter"}, {"vid": 648, "value": "Spring/Summer/Fall"}], "required": true, "isSale": false}, {"pid": 4, "templateModuleId": 278182, "templatePid": 1464661, "refPid": 20, "name": "Operation Instruction", "valueUnitList": null, "values": [{"vid": 165, "value": "Hand wash or professional dry clean"}, {"vid": 166, "value": "Do not wash"}, {"vid": 169, "value": "Dry clean"}, {"vid": 26001, "value": "Machine wash, do not dry clean"}, {"vid": 26002, "value": "Hand wash,do not dry clean"}, {"vid": 26003, "value": "machine washable, no dry clean"}, {"vid": 26004, "value": "Machine wash or professional dry clean"}, {"vid": 3805, "value": "<PERSON>"}], "required": true, "isSale": false}, {"pid": 3, "templateModuleId": 278182, "templatePid": 1464662, "refPid": 19, "name": "Style", "valueUnitList": null, "values": [{"vid": 32855, "value": "Y2K"}, {"vid": 136, "value": "Vintage"}, {"vid": 137, "value": "Modest"}, {"vid": 140, "value": "Elegant"}, {"vid": 144, "value": "Sexy"}, {"vid": 145, "value": "Casual"}, {"vid": 148, "value": "Cute"}, {"vid": 150, "value": "Vacation"}, {"vid": 57691, "value": "Elegant"}, {"vid": 64099, "value": "Middle-eastern"}, {"vid": 64670, "value": "YOUNG"}, {"vid": 81519, "value": "Chic"}], "required": true, "isSale": false}, {"pid": 36, "templateModuleId": 278182, "templatePid": 1464663, "refPid": 114, "name": "Fit Type", "valueUnitList": null, "values": [{"vid": 1839, "value": "<PERSON><PERSON>"}, {"vid": 35188, "value": "Loose"}, {"vid": 35189, "value": "Regular"}, {"vid": 35190, "value": "Oversized"}], "required": false, "isSale": false}, {"pid": 1364, "templateModuleId": 278182, "templatePid": 1464664, "refPid": 1352, "name": "Fabric <PERSON>asticity", "valueUnitList": null, "values": [{"vid": 35196, "value": "No Elasticity"}, {"vid": 35197, "value": "Micro Elasticity"}, {"vid": 35198, "value": "Mid Elasticity"}, {"vid": 35199, "value": "High Elasticity"}], "required": true, "isSale": false}, {"pid": 1437, "templateModuleId": 278182, "templatePid": 1464665, "refPid": 1919, "name": "Printing Type", "valueUnitList": null, "values": [{"vid": 36892, "value": "No Printing"}, {"vid": 36893, "value": "Positioning Printing"}, {"vid": 36894, "value": "Random Printing"}], "required": true, "isSale": false}, {"pid": 1467, "templateModuleId": 278182, "templatePid": 1464666, "refPid": 1960, "name": "Brand", "valueUnitList": null, "values": [{"vid": 279111, "value": "招商备案TEST001", "group": null, "brandId": 110033837004, "additionalInfo": null}, {"vid": 279094, "value": "TEST20250618", "group": null, "brandId": 110033837002, "additionalInfo": null}, {"vid": 279082, "value": "玉子测试1010", "group": null, "brandId": 110033807037, "additionalInfo": null}, {"vid": 237770, "value": "TEST202504272043000", "group": null, "brandId": 110032900002, "additionalInfo": null}, {"vid": 220343, "value": "测试品牌", "group": null, "brandId": 110032872001, "additionalInfo": null}, {"vid": 212090, "value": "YUZITEST", "group": null, "brandId": 110032805002, "additionalInfo": null}, {"vid": 211856, "value": "范思文测试", "group": null, "brandId": 110032603002, "additionalInfo": null}, {"vid": 206900, "value": "范思文测试品牌", "group": null, "brandId": 110030811002, "additionalInfo": null}, {"vid": 125011, "value": "玉子测试品牌", "group": null, "brandId": 110019033591, "additionalInfo": null}, {"vid": 205012, "value": "11", "group": null, "brandId": 110017083376, "additionalInfo": null}, {"vid": 118429, "value": "玉子测试专用品牌1", "group": null, "brandId": 110016314656, "additionalInfo": null}, {"vid": 200502, "value": "渣科测试品牌4", "group": null, "brandId": 110016309790, "additionalInfo": null}, {"vid": 116937, "value": "WINSEN-TEST-3", "group": null, "brandId": 110000010439, "additionalInfo": null}, {"vid": 75727, "value": "WINSEN-TEST-3", "group": null, "brandId": 10007, "additionalInfo": null}, {"vid": 83406, "value": "TN-20240608001", "group": null, "brandId": 15010, "additionalInfo": null}, {"vid": 120702, "value": "TESTYUMI", "group": null, "brandId": 60918259, "additionalInfo": null}], "required": false, "isSale": false}, {"pid": 1514, "templateModuleId": 278182, "templatePid": 1464667, "refPid": 2103, "name": "Style source", "valueUnitList": null, "values": [{"vid": 70452, "value": "Customized"}, {"vid": 70453, "value": "Stock"}], "required": true, "isSale": false}, {"pid": 1224, "templateModuleId": 278182, "templatePid": 1464668, "refPid": 1192, "name": "Weaving Method", "valueUnitList": null, "values": [{"vid": 29810, "value": "Woven"}, {"vid": 54654, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 54655, "value": "Non-textile materials"}, {"vid": 54746, "value": "Non-woven Fabric"}], "required": true, "isSale": false}, {"pid": 5, "templateModuleId": 278182, "templatePid": 1464669, "refPid": 21, "name": "Collar Style", "valueUnitList": null, "values": [{"vid": 170, "value": "Off the Shoulder"}, {"vid": 171, "value": "One Shoulder"}, {"vid": 172, "value": "Hooded"}, {"vid": 173, "value": "Crew Neck"}, {"vid": 174, "value": "V-neck"}, {"vid": 175, "value": "High neck"}, {"vid": 176, "value": "Asymmetrical Neck"}, {"vid": 177, "value": "Criss cross neck"}, {"vid": 178, "value": "Boat"}, {"vid": 179, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 180, "value": "Lapel"}, {"vid": 181, "value": "Cowl"}, {"vid": 182, "value": "Crew"}, {"vid": 183, "value": "Deep V Neck"}, {"vid": 184, "value": "Drape Neck"}, {"vid": 185, "value": "Funnel"}, {"vid": 186, "value": "Halter Neck"}, {"vid": 187, "value": "Lapel"}, {"vid": 188, "value": "Low"}, {"vid": 189, "value": "Mandarin Collar"}, {"vid": 190, "value": "Notched"}, {"vid": 191, "value": "<PERSON>"}, {"vid": 192, "value": "Scoop Neck"}, {"vid": 193, "value": "Spaghetti Strap"}, {"vid": 194, "value": "Square neck"}, {"vid": 195, "value": "Stand Collar"}, {"vid": 196, "value": "Bandeau"}, {"vid": 197, "value": "Strappy back"}, {"vid": 198, "value": "Sweetheart"}, {"vid": 199, "value": "Vest"}, {"vid": 200, "value": "<PERSON><PERSON>"}, {"vid": 201, "value": "Keyhole"}, {"vid": 202, "value": "Baseball Collar"}, {"vid": 203, "value": "Sailor collar neck"}, {"vid": 204, "value": "Polo"}, {"vid": 1054, "value": "<PERSON><PERSON>"}, {"vid": 1055, "value": "Contrast Lapel"}, {"vid": 1056, "value": "Wide"}, {"vid": 1122, "value": "Peak Lapels"}, {"vid": 1123, "value": "Cardigan Collar"}, {"vid": 1150, "value": "<PERSON><PERSON>"}, {"vid": 1213, "value": "Jewel Neckline"}, {"vid": 1214, "value": "Contrast Collar"}, {"vid": 1216, "value": "Heart"}, {"vid": 1461, "value": "<PERSON>lar<PERSON>"}, {"vid": 11895, "value": "One shoulder neck"}, {"vid": 11896, "value": "Halter Neck"}, {"vid": 11897, "value": "Asymmetric neck"}, {"vid": 11898, "value": "Keyhole neck"}, {"vid": 11899, "value": "Sweetheart neck"}, {"vid": 11900, "value": "Conventional"}, {"vid": 11901, "value": "Hooded"}, {"vid": 11902, "value": "Collared neck"}, {"vid": 11903, "value": "Notch neck"}, {"vid": 11904, "value": "<PERSON> neck"}, {"vid": 11905, "value": "Scoop neck"}], "required": false, "isSale": false}, {"pid": 1891, "templateModuleId": 278182, "templatePid": 1464670, "refPid": 6227, "name": "Collection", "valueUnitList": null, "values": [{"vid": 74549, "value": "Middle East"}, {"vid": 74550, "value": "Asian"}, {"vid": 74551, "value": "Elegant"}, {"vid": 204623, "value": "Latin America"}], "required": false, "isSale": false}, {"pid": 2054, "templateModuleId": 278182, "templatePid": 1464671, "refPid": 6926, "name": "Fabric Texture 1", "valueUnitList": null, "values": [{"vid": 161198, "value": "Smooth fabric"}, {"vid": 161199, "value": "Suede/PU"}], "required": true, "isSale": false}, {"pid": 2054, "templateModuleId": 278182, "templatePid": 1464672, "refPid": 6927, "name": "Fabric Texture 2", "valueUnitList": null, "values": [{"vid": 161198, "value": "Smooth fabric"}, {"vid": 161199, "value": "Suede/PU"}], "required": false, "isSale": false}, {"pid": 2050, "templateModuleId": 278182, "templatePid": 1464673, "refPid": 6928, "name": "Lining Texture", "valueUnitList": null, "values": [{"vid": 161110, "value": "Smooth fabric"}, {"vid": 161111, "value": "Suede/PU"}, {"vid": 161112, "value": "No lining"}], "required": true, "isSale": false}, {"pid": 2052, "templateModuleId": 278182, "templatePid": 1464674, "refPid": 6930, "name": "Fabric Weight 1 (g/m²)", "propertyValueType": 1, "valueUnitList": [{"valueUnit": "g/㎡", "valueUnitId": 240}], "values": null, "required": true, "isSale": false}, {"pid": 2052, "templateModuleId": 278182, "templatePid": 1464675, "refPid": 6931, "name": "Fabric Weight 2 (g/m²)", "propertyValueType": 1, "valueUnitList": [{"valueUnit": "g/㎡", "valueUnitId": 240}], "values": null, "required": true, "isSale": false}, {"pid": 2052, "templateModuleId": 278182, "templatePid": 1464676, "refPid": 6934, "name": "Lining Weight (g/m²)", "propertyValueType": 1, "valueUnitList": [{"valueUnit": "g/㎡", "valueUnitId": 240}], "values": null, "required": true, "isSale": false}, {"pid": 2, "templateModuleId": 278182, "templatePid": 1464677, "refPid": 6547, "name": "Lining Ingredients", "valueUnitList": [{"valueUnit": "%", "valueUnitId": 57}], "values": [{"vid": 35392, "value": "Silk"}, {"vid": 35393, "value": "Linen"}, {"vid": 35394, "value": "Modal"}, {"vid": 35395, "value": "Viscose"}, {"vid": 35396, "value": "Rayon"}, {"vid": 35397, "value": "Carbon"}, {"vid": 35398, "value": "Metal"}, {"vid": 35399, "value": "Wool"}, {"vid": 35400, "value": "Cashmere"}, {"vid": 35401, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 35403, "value": "<PERSON> Hair"}, {"vid": 35404, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 35405, "value": "<PERSON><PERSON>"}, {"vid": 35406, "value": "<PERSON> Leather"}, {"vid": 35407, "value": "Polyurethane"}, {"vid": 35408, "value": "Polyvinyl Chloride"}, {"vid": 35409, "value": "Cupro"}, {"vid": 35410, "value": "Lyocell"}, {"vid": 35411, "value": "Acetate"}, {"vid": 35412, "value": "Down"}, {"vid": 35413, "value": "<PERSON><PERSON>"}, {"vid": 35414, "value": "Duck Down"}, {"vid": 35415, "value": "Alpaca"}, {"vid": 36254, "value": "Other Fibers"}, {"vid": 98, "value": "Polyester"}, {"vid": 35364, "value": "Metallized Fibres"}, {"vid": 47217, "value": "Polypropylene fibers"}, {"vid": 121, "value": "<PERSON><PERSON>"}, {"vid": 35385, "value": "Nylon"}, {"vid": 35386, "value": "Polyester"}, {"vid": 35387, "value": "Polyamide"}, {"vid": 35388, "value": "<PERSON><PERSON><PERSON>"}, {"vid": 35389, "value": "Spandex"}, {"vid": 35390, "value": "Acrylic"}, {"vid": 35391, "value": "Cotton"}], "required": false, "isSale": false}], "goodsSpecProperties": [{"pid": 14, "templateModuleId": 278181, "templatePid": 1464645, "refPid": 65, "name": "Size", "valueUnitList": null, "values": [{"vid": 311, "value": "XXS", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 10002}, {"vid": 313, "value": "XS", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 12001}, {"vid": 315, "value": "S", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 10004}, {"vid": 317, "value": "M", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 9005}, {"vid": 319, "value": "L", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 11002}, {"vid": 320, "value": "XL", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 12003}, {"vid": 321, "value": "XXL", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 10, "name": "Alpha"}, "specId": 8002}, {"vid": 29161, "value": "one-size", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 33228}, {"vid": 32817, "value": "Petite One-size", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 3, "name": "Petite size"}, "specId": 43686963}, {"vid": 310, "value": "petite XXS", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 3, "name": "Petite size"}, "specId": 9002}, {"vid": 312, "value": "petite XS", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 3, "name": "Petite size"}, "specId": 10003}, {"vid": 314, "value": "petite S", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 3, "name": "Petite size"}, "specId": 9003}, {"vid": 316, "value": "petite M", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 3, "name": "Petite size"}, "specId": 9004}, {"vid": 318, "value": "petite L", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 3, "name": "Petite size"}, "specId": 12002}, {"vid": 35035, "value": "Asian XS", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 16, "name": "Alpha"}, "specId": 45209576}, {"vid": 35036, "value": "Asian S", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 16, "name": "Alpha"}, "specId": 45208651}, {"vid": 35037, "value": "Asian M", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 16, "name": "Alpha"}, "specId": 45210502}, {"vid": 35038, "value": "Asian L", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 16, "name": "Alpha"}, "specId": 45210503}, {"vid": 35039, "value": "Asian XL", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 16, "name": "Alpha"}, "specId": 45208652}, {"vid": 35040, "value": "Asian XXL", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 16, "name": "Alpha"}, "specId": 45210504}, {"vid": 35080, "value": "Asian One-size", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 14, "name": "One-size"}, "specId": 45417227}, {"vid": 79431, "value": "Asian S/M", "group": {"id": 20000, "name": "Asian size"}, "subGroup": {"id": 14, "name": "One-size"}, "specId": 74656706}, {"vid": 79430, "value": "S/M", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 22807}, {"vid": 81210, "value": "S/ M", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 50939283}, {"vid": 345, "value": "4", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 19464}, {"vid": 349, "value": "6", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 19358}, {"vid": 72153, "value": "8", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 20365}, {"vid": 72157, "value": "10", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 20367}, {"vid": 72161, "value": "12", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 18026}, {"vid": 72165, "value": "14", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 19784}, {"vid": 72169, "value": "16", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 17003}, {"vid": 72262, "value": "18", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 17004}, {"vid": 72266, "value": "20", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 18005}, {"vid": 239663, "value": "22", "group": {"id": 100000, "name": "UK size"}, "subGroup": {"id": 7, "name": "Numeric"}, "specId": 17006}, {"vid": 36827, "value": "XS/S", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 2442461}, {"vid": 36826, "value": "S/M", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 22807}, {"vid": 36828, "value": "M/L", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 119184}, {"vid": 36830, "value": "L/XL", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 21899}, {"vid": 36829, "value": "XL/XXL", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 8187495}, {"vid": 240836, "value": "XL/2XL", "group": {"id": 10000, "name": "US/EU size"}, "subGroup": {"id": 1, "name": "One Size"}, "specId": 31721596}], "required": true, "isSale": true}, {"pid": 13, "templateModuleId": 278181, "templatePid": 1464646, "refPid": 63, "name": "Color", "valueUnitList": null, "values": [{"vid": 376, "value": "White", "group": {"id": 1, "name": "White Color Family"}, "specId": 2001}, {"vid": 377, "value": "Red", "group": {"id": 4, "name": "Red Color Family"}, "specId": 2}, {"vid": 378, "value": "Black", "group": {"id": 3, "name": "Black Color Family"}, "specId": 3002}, {"vid": 433, "value": "Beige", "group": {"id": 1, "name": "White Color Family"}, "specId": 15060}, {"vid": 434, "value": "Creamy White", "group": {"id": 1, "name": "White Color Family"}, "specId": 16053}, {"vid": 435, "value": "<PERSON>", "group": {"id": 1, "name": "White Color Family"}, "specId": 16054}, {"vid": 436, "value": "Orange-Red", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16055}, {"vid": 437, "value": "Rose Red", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16056}, {"vid": 438, "value": "Pink", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16057}, {"vid": 439, "value": "Peach", "group": {"id": 4, "name": "Red Color Family"}, "specId": 15061}, {"vid": 440, "value": "<PERSON> rose", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16058}, {"vid": 441, "value": "Deep Pink", "group": {"id": 4, "name": "Red Color Family"}, "specId": 15062}, {"vid": 442, "value": "<PERSON>", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16059}, {"vid": 443, "value": "Lotus pink", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16060}, {"vid": 444, "value": "Watermelon Red", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16061}, {"vid": 445, "value": "Burgundy", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16062}, {"vid": 446, "value": "Scarlet", "group": {"id": 4, "name": "Red Color Family"}, "specId": 15063}, {"vid": 447, "value": "Bright Pink", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16063}, {"vid": 448, "value": "Ma<PERSON><PERSON>", "group": {"id": 4, "name": "Red Color Family"}, "specId": 15064}, {"vid": 449, "value": "Coral", "group": {"id": 4, "name": "Red Color Family"}, "specId": 15065}, {"vid": 450, "value": "Orange", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16064}, {"vid": 451, "value": "Brick Red", "group": {"id": 4, "name": "Red Color Family"}, "specId": 16065}, {"vid": 452, "value": "Deep red", "group": {"id": 4, "name": "Red Color Family"}, "specId": 15066}, {"vid": 453, "value": "Multicolor", "group": {"id": 12, "name": "Multicolor Color Family"}, "specId": 16066}, {"vid": 454, "value": "Khaki", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 15067}, {"vid": 455, "value": "<PERSON><PERSON><PERSON>", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16067}, {"vid": 456, "value": "Bright Yellow", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 15068}, {"vid": 457, "value": "Apricot", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16068}, {"vid": 458, "value": "Lemon Yellow", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 15069}, {"vid": 459, "value": "Fluorescent Yellow", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16069}, {"vid": 460, "value": "Golden", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16070}, {"vid": 461, "value": "Champagne", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16071}, {"vid": 462, "value": "Yellow", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16072}, {"vid": 463, "value": "Light Yellow", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16073}, {"vid": 464, "value": "Wheat", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16074}, {"vid": 465, "value": "Orange", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16075}, {"vid": 466, "value": "Earthy yellow", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 15070}, {"vid": 467, "value": "Sunflower Color", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16076}, {"vid": 468, "value": "Light Beige", "group": {"id": 5, "name": "Yellow Color Family"}, "specId": 16077}, {"vid": 469, "value": "Dark Grey", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 15071}, {"vid": 470, "value": "Light Grey", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 16078}, {"vid": 471, "value": "<PERSON>", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 16079}, {"vid": 472, "value": "Grey", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 16080}, {"vid": 473, "value": "Lead Color", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 16081}, {"vid": 474, "value": "<PERSON><PERSON>", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 15072}, {"vid": 475, "value": "<PERSON><PERSON>", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 15073}, {"vid": 476, "value": "Space Gray", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 16082}, {"vid": 477, "value": "Graphite Color", "group": {"id": 2, "name": "Gray Color Family"}, "specId": 16083}, {"vid": 478, "value": "Sky Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16084}, {"vid": 479, "value": "Royal blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15074}, {"vid": 480, "value": "Royal Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15075}, {"vid": 481, "value": "Light Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16085}, {"vid": 482, "value": "Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16086}, {"vid": 483, "value": "Baby blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15076}, {"vid": 484, "value": "Bright Steel Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15077}, {"vid": 485, "value": "VibrantBlue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16087}, {"vid": 486, "value": "Sky Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16084}, {"vid": 487, "value": "Deep Sky Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15078}, {"vid": 488, "value": "Military Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15079}, {"vid": 489, "value": "Azure", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15080}, {"vid": 490, "value": "Light Cyan", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16088}, {"vid": 491, "value": "<PERSON><PERSON>", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16089}, {"vid": 492, "value": "<PERSON>", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16090}, {"vid": 493, "value": "Navy Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16091}, {"vid": 494, "value": "Pastel Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 16092}, {"vid": 495, "value": "Peacock Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15074}, {"vid": 496, "value": "Sea Blue", "group": {"id": 7, "name": "Blue Color Family"}, "specId": 15081}, {"vid": 497, "value": "Army Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16093}, {"vid": 498, "value": "Dark Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15082}, {"vid": 499, "value": "Light Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15083}, {"vid": 500, "value": "Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15084}, {"vid": 501, "value": "Emerald Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16094}, {"vid": 502, "value": "<PERSON>", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15085}, {"vid": 503, "value": "Fluorescent Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15086}, {"vid": 504, "value": "Greenish Yellow", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16095}, {"vid": 505, "value": "Grass Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15087}, {"vid": 506, "value": "Yellow-green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15088}, {"vid": 507, "value": "Viridity", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15089}, {"vid": 508, "value": "Emerald", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16096}, {"vid": 509, "value": "Ocean Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16097}, {"vid": 510, "value": "<PERSON>", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15090}, {"vid": 511, "value": "<PERSON>", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16098}, {"vid": 512, "value": "Matcha Color", "group": {"id": 6, "name": "Green Color Family"}, "specId": 16099}, {"vid": 513, "value": "Malachite Green", "group": {"id": 6, "name": "Green Color Family"}, "specId": 15085}, {"vid": 514, "value": "Transparent", "group": {"id": 10, "name": "Transparent Color Family"}, "specId": 15091}, {"vid": 515, "value": "Light Purple", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15092}, {"vid": 516, "value": "Deep purple", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15093}, {"vid": 517, "value": "Fuchsia", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15094}, {"vid": 518, "value": "Violet", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15095}, {"vid": 519, "value": "Dark Violet", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15096}, {"vid": 520, "value": "Purple", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 16100}, {"vid": 521, "value": "<PERSON> Magenta", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15097}, {"vid": 522, "value": "Violet Blue", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 15098}, {"vid": 523, "value": "Lavender", "group": {"id": 8, "name": "Purple Color Family"}, "specId": 16101}, {"vid": 524, "value": "Coffee", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16102}, {"vid": 525, "value": "Chocolate", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15099}, {"vid": 526, "value": "Maroon", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15100}, {"vid": 527, "value": "Dark Khaki", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16103}, {"vid": 528, "value": "<PERSON> Brown", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15101}, {"vid": 529, "value": "<PERSON>", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15102}, {"vid": 530, "value": "<PERSON>", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16104}, {"vid": 531, "value": "<PERSON>", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15103}, {"vid": 532, "value": "Camel", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16105}, {"vid": 533, "value": "<PERSON>", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16106}, {"vid": 534, "value": "Reddish Brown", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16107}, {"vid": 535, "value": "Tawny", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15104}, {"vid": 536, "value": "Linen", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16108}, {"vid": 537, "value": "<PERSON><PERSON>", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15105}, {"vid": 538, "value": "Amber", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16109}, {"vid": 539, "value": "Adzuki Bean", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15106}, {"vid": 540, "value": "Rust", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16110}, {"vid": 541, "value": "Cinnamon", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15107}, {"vid": 542, "value": "<PERSON>", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16111}, {"vid": 543, "value": "<PERSON> golden", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 15108}, {"vid": 544, "value": "Bronze", "group": {"id": 9, "name": "Brown Color Family"}, "specId": 16112}, {"vid": 26419, "value": "Mixed Color", "group": {"id": 11, "name": "Other"}, "specId": 21263}, {"vid": 32560, "value": "Deep Grey", "group": {"id": 3, "name": "Black Color Family"}, "specId": 22028}], "required": true, "isSale": true}]}